const express = require('express');
const cors = require('cors');
const serverless = require('serverless-http');
require('dotenv').config();

// Import database connection
const { sequelize, testConnection } = require('../src/config/database');

// Import routes (to be created)
const authRoutes = require('../src/routes/auth.routes');
const userRoutes = require('../src/routes/user.routes');
const companyRoutes = require('../src/routes/company.routes');
const projectRoutes = require('../src/routes/project.routes');
const taskRoutes = require('../src/routes/task.routes');
const attendanceRoutes = require('../src/routes/attendance.routes');
const leaveRoutes = require('../src/routes/leave.routes');
const evaluationRoutes = require('../src/routes/evaluation.routes');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/companies', companyRoutes);
app.use('/api/projects', projectRoutes);
app.use('/api/tasks', taskRoutes);
app.use('/api/attendance', attendanceRoutes);
app.use('/api/leaves', leaveRoutes);
app.use('/api/evaluations', evaluationRoutes);

// Root route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to TeamCheck API' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Something went wrong!',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Internal Server Error'
  });
});

// Start server
const startServer = async () => {
  try {
    // Test database connection
    await testConnection();

    // Sync models with database (create tables if they don't exist)
    console.log('Syncing models with database...');
    await sequelize.sync();
    console.log('Database sync completed successfully!');

    // Start the server
    app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

// Run the server
// startServer();

module.exports = serverless(app);
