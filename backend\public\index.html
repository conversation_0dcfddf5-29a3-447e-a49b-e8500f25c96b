<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TeamCheck API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .endpoint {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .method {
            font-weight: bold;
            color: #007bff;
        }
        a {
            color: #007bff;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 TeamCheck API</h1>
        <p>Welcome to the TeamCheck API backend. This is a RESTful API for team management.</p>
        
        <h2>📋 Available Endpoints</h2>
        
        <div class="endpoint">
            <span class="method">GET</span> <a href="/health">/health</a> - API health check
        </div>
        
        <div class="endpoint">
            <span class="method">POST</span> /auth/register - Register a new user
        </div>
        
        <div class="endpoint">
            <span class="method">POST</span> /auth/login - Login user
        </div>
        
        <div class="endpoint">
            <span class="method">GET</span> /auth/me - Get current user (requires auth)
        </div>
        
        <div class="endpoint">
            <span class="method">GET</span> /companies - Get companies (requires auth)
        </div>
        
        <div class="endpoint">
            <span class="method">GET</span> /users - Get users (requires auth)
        </div>
        
        <div class="endpoint">
            <span class="method">GET</span> /projects - Get projects (requires auth)
        </div>
        
        <div class="endpoint">
            <span class="method">GET</span> /tasks - Get tasks (requires auth)
        </div>
        
        <h2>🔗 Quick Links</h2>
        <ul>
            <li><a href="/health">Health Check</a></li>
            <li><a href="https://github.com/your-username/teamcheck" target="_blank">GitHub Repository</a></li>
            <li><a href="https://your-frontend-url.com" target="_blank">Frontend Application</a></li>
        </ul>
        
        <h2>📚 Documentation</h2>
        <p>For detailed API documentation, please refer to the README.md file in the repository.</p>
        
        <footer style="text-align: center; margin-top: 40px; color: #666;">
            <p>TeamCheck API v1.0.0</p>
        </footer>
    </div>
</body>
</html>
