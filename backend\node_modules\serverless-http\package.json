{"name": "serverless-http", "version": "3.2.0", "description": "Use existing web application frameworks in serverless environments", "main": "serverless-http.js", "types": "serverless-http.d.ts", "engines": {"node": ">=12.0"}, "directories": {"test": "test"}, "repository": {"type": "git", "url": "https://github.com/dougmoscrop/serverless-http"}, "scripts": {"pretest": "tsc --strict --skipLib<PERSON>heck --noEmit test/typecheck.ts", "test": "nyc mocha", "posttest": "eslint lib test", "test:integration": "mocha test/integration/test.js", "postpublish": "git push origin master --tags"}, "keywords": ["serverless", "serverless applications", "koa", "express", "connect", "api gateway", "lambda", "aws", "aws lambda", "amazon", "amazon web services"], "author": "<PERSON> <<EMAIL>> (http://www.github.com/dougmoscrop)", "contributors": ["<PERSON> (https://github.com/dougmoscrop)", "<PERSON> (https://github.com/kgroat)", "<PERSON> (https://github.com/bsdkurt)", "Roch Devost (https://github.com/rochdev)", "<PERSON> (https://github.com/bdgamble)", "<PERSON> (https://github.com/demacdonald)", "<PERSON><PERSON> (https://github.com/panva)", "<PERSON> (https://github.com/ktonon)", "<PERSON> (https://github.com/mvayngrib)", "<PERSON><PERSON> (https://github.com/tamasmahr)", "<PERSON> (https://github.com/daniel-ac-martin)"], "homepage": "https://github.com/dougmoscrop/serverless-http", "license": "MIT", "devDependencies": {"@loopback/rest": "^11.1.2", "@types/koa": "^2.11.0", "body-parser": "^1.19.0", "chai": "^4.2.0", "chai-as-promised": "^7.1.1", "cookie-parser": "^1.4.4", "eslint": "^8.12.0", "eslint-plugin-mocha": "^10.0.3", "express": "^4.17.1", "fastify": "^3.27.4", "get-stream": "^5.1.0", "hapi": "^18.1.0", "inversify": "^6.0.1", "inversify-express-utils": "^6.3.2", "koa": "^2.11.0", "koa-bodyparser": "^4.2.1", "koa-compress": "^5.1.0", "koa-route": "^3.2.0", "koa-router": "^10.1.1", "koa-static": "^5.0.0", "lambda-log": "^3.1.0", "mocha": "^9.2.2", "morgan": "^1.9.1", "nyc": "^15.0.0", "on-finished": "^2.3.0", "on-headers": "^1.0.2", "polka": "^0.5.2", "reflect-metadata": "^0.1.13", "restana": "^4.0.7", "sails": "^1.2.3", "serverless": "^3.10.2", "serverless-offline": "^8.5.0", "serverless-plugin-common-excludes": "^4.0.0", "serverless-plugin-custom-binary": "^2.0.0", "serverless-plugin-include-dependencies": "^5.0.0", "sinon": "^13.0.1", "supertest": "^6.2.2", "typescript": "^4.6.3"}}